# Dependencies
node_modules
client/node_modules

# Development files
dev.ts
*.log
*.logs

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Build artifacts (except dist/api and dist/public)
dist/index.js

# Database and migration files
migrations/
*.sql
recreate_tables.sql
remove_foreign_keys.sql

# Documentation and guides
*.md
docs/
attached_assets/

# Scripts and utilities
scripts/
build-optimize.js
novo_endpoint_taxa_entrega.js
test-payment-status.js

# Firebase files (keep only necessary ones)
firebase-service-account.json
firebase-init.sh
firebase.hosting.json

# Temporary files
temp/
*.tmp

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Vercel files
.vercel/

# TypeScript build info
*.tsbuildinfo

# Drizzle files
drizzle.config.ts

# Memory and debug files
memories.md
github_commands.md
setup-manual-instructions.md

# Deployment files
deploy.sh
DEPLOYMENT.md
FIREBASE_HOSTING.md

# Debug and implementation guides
*_DEBUG*.md
*_GUIDE*.md
*_IMPLEMENTATION*.md
PLANO_*.md
