{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "engines": {"node": "20.x"}, "scripts": {"dev": "cross-env NODE_ENV=development tsx dev.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist/api", "start": "cross-env NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "setup:subscriptions": "node scripts/setup-subscriptions.js setup", "subscriptions:status": "node scripts/setup-subscriptions.js status", "test:subscriptions": "node scripts/test-subscription-system.js", "check:subscriptions": "node scripts/check-subscription-config.js", "setup:global-admin": "node scripts/add-global-admin-field.js", "promote:global-admin": "node scripts/promote-global-admin.js", "list:users": "node scripts/promote-global-admin.js list", "test:global-admin": "node scripts/test-global-admin.js"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@sendgrid/mail": "^8.1.5", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.60.5", "@types/multer": "^1.4.12", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "eruda": "^3.4.1", "express": "^4.21.2", "express-session": "^1.18.1", "firebase": "^11.6.1", "firebase-admin": "^13.3.0", "firebase-tools": "^14.2.1", "form-data": "^4.0.2", "framer-motion": "^11.13.1", "html2canvas": "^1.4.1", "i18next": "^25.0.1", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "lucide-react": "^0.453.0", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^1.4.5-lts.2", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openid-client": "^6.4.2", "passport": "^0.7.0", "passport-local": "^1.0.0", "postgres": "^3.4.5", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-i18next": "^15.5.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "stripe": "^18.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.2", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}