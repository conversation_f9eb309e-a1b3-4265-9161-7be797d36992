import React, { useEffect, useState } from 'react';
import { X, ShoppingCart } from 'lucide-react';
import { useCart } from '@/context/CartContext';
import { useLocation } from 'wouter';
import ProductCard from '../ProductCard';
import ProductDetailModal from '../ProductDetailModal';
import { Product, Category, Store } from '@shared/schema';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface IOSLayoutProps {
  store: Store;
  categories: Category[];
  products: Product[];
  onProductClick: (product: Product) => void;
  onAddToCart: (product: Product) => void;
}

const IOSLayout: React.FC<IOSLayoutProps> = ({
  store,
  categories,
  products,
  onProductClick,
  onAddToCart
}) => {
  const { items, totalItems, total } = useCart();
  const [, navigate] = useLocation();
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Inicializar com a primeira categoria, se houver
  useEffect(() => {
    if (categories && categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0].id);
    }
  }, [categories, selectedCategory]);

  // Filtrar produtos quando a categoria mudar
  useEffect(() => {
    if (selectedCategory === null) {
      setFilteredProducts(products);
    } else {
      setFilteredProducts(products.filter(p => p.categoryId === selectedCategory));
    }
  }, [selectedCategory, products]);

  const handleProductClick = (product: Product) => {
    onProductClick(product);
    setSelectedProduct(product);
    setIsDetailOpen(true);
  };

  const handleCloseDetail = () => {
    setIsDetailOpen(false);
    setSelectedProduct(null);
  };

  const handleCategoryClick = (categoryId: number) => {
    setSelectedCategory(categoryId);
  };

  const handleCartClick = () => {
    navigate('/cart');
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header com logo da loja */}
      <div 
        className="relative w-full bg-gradient-to-r from-primary/90 to-primary p-4 text-primary-foreground flex items-center justify-between shadow-md"
        style={{ 
          backgroundImage: store?.headerImage ? `linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.7)), url(${store.headerImage})` : undefined,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        <div className="flex items-center">
          {store?.logo && (
            <img 
              src={store.logo} 
              alt={store?.name || 'Store'} 
              className="h-10 w-10 rounded-full mr-3 object-cover border-2 border-white shadow-sm" 
            />
          )}
          <h1 className="text-xl font-bold">{store?.name}</h1>
        </div>
        <div className="relative">
          <Button 
            variant="outline" 
            size="icon" 
            className="bg-white text-primary rounded-full" 
            onClick={handleCartClick}
          >
            <ShoppingCart className="h-5 w-5" />
            {totalItems > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {totalItems}
              </span>
            )}
          </Button>
        </div>
      </div>

      {/* Carrossel horizontal de categorias estilo iOS */}
      <div className="px-4 py-3 border-b sticky top-0 bg-background z-10">
        <div className="flex space-x-2 overflow-x-auto scrollbar-hide pb-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category.id)}
              className={`flex-shrink-0 px-4 py-2 rounded-full border font-medium transition-all ${selectedCategory === category.id
                ? 'bg-primary text-primary-foreground border-primary shadow-sm'
                : 'bg-background border-border text-foreground hover:bg-muted'}`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Lista de produtos da categoria selecionada */}
      <div className="flex-grow overflow-auto p-4">
        {selectedCategory && (
          <div className="mb-4">
            <h2 className="text-lg font-semibold mb-2">
              {categories.find(c => c.id === selectedCategory)?.name}
            </h2>
            <div className="grid grid-cols-2 gap-4">
              {filteredProducts.map((product) => {
                // Converter o tipo do banco para o tipo esperado pelo componente
                const productForCard: Product = {
                  ...product,
                  description: product.description || undefined
                };
                return (
                  <ProductCard
                    key={product.id}
                    product={productForCard}
                    onClick={() => handleProductClick(productForCard)}
                  />
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Carrinho flutuante no rodapé */}
      {totalItems > 0 && (
        <div className="sticky bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-background to-transparent">
          <Card className="w-full bg-primary text-primary-foreground p-3 rounded-xl shadow-lg">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium">{totalItems} {totalItems === 1 ? 'item' : 'itens'}</p>
                <p className="text-lg font-bold">{store?.currency || 'R$'} {total.toFixed(2)}</p>
              </div>
              <Button 
                variant="secondary" 
                className="rounded-full" 
                onClick={handleCartClick}
              >
                Ver carrinho
              </Button>
            </div>
          </Card>
        </div>
      )}

      {/* Modal de detalhes do produto */}
      {isDetailOpen && selectedProduct && (
        <ProductDetailModal
          product={{
            ...selectedProduct,
            description: selectedProduct.description || undefined
          }}
          open={isDetailOpen}
          onOpenChange={(open) => {
            if (!open) handleCloseDetail();
          }}
        />
      )}
    </div>
  );
};

export default IOSLayout;
