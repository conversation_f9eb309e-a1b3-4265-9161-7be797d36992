#!/bin/bash

# Script para deployment no Vercel
# Uso: ./vercel-deploy.sh [preview|production]

set -e

echo "🚀 Iniciando deployment no Vercel..."

# Verificar se o Vercel CLI está instalado
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI não encontrado. Instale com: npm i -g vercel"
    exit 1
fi

# Verificar se está logado no Vercel
if ! vercel whoami &> /dev/null; then
    echo "❌ Não está logado no Vercel. Execute: vercel login"
    exit 1
fi

# Limpar builds anteriores
echo "🧹 Limpando builds anteriores..."
rm -rf dist/

# Build do projeto
echo "🔨 Fazendo build do projeto..."
npm run build

# Verificar se o build foi bem-sucedido
if [ ! -d "dist/public" ] || [ ! -f "dist/api/index.js" ]; then
    echo "❌ Build falhou. Verifique os erros acima."
    exit 1
fi

echo "✅ Build concluído com sucesso!"

# Determinar tipo de deployment
DEPLOY_TYPE=${1:-preview}

if [ "$DEPLOY_TYPE" = "production" ] || [ "$DEPLOY_TYPE" = "prod" ]; then
    echo "🌟 Fazendo deployment para PRODUCTION..."
    vercel --prod
else
    echo "🔍 Fazendo deployment para PREVIEW..."
    vercel
fi

echo "✅ Deployment concluído!"
echo "📋 Para ver os logs: vercel logs [URL_DO_DEPLOYMENT]"
echo "📊 Para ver status: vercel ls"
