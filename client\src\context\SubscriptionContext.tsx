import { createContext, useContext, ReactNode } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/context/FirebaseAuthContext';
import { 
  type Subscription, 
  type PlanConfig, 
  type PlanType,
  PLAN_CONFIGS,
  isFeatureAvailable,
  getFeatureLimit,
  isLimitExceeded
} from '@shared/schema';

interface UsageInfo {
  subscription: Subscription | null;
  planConfig: PlanConfig;
  usage: {
    products: {
      current: number;
      limit: number;
      isLimitExceeded: boolean;
    };
    orders: {
      current: number;
      limit: number;
      isLimitExceeded: boolean;
    };
  };
  features: {
    maxProducts: number;
    maxOrdersPerMonth: number;
    allowPdfGeneration: boolean;
    allowAnalytics: boolean;
    allowWhatsappIntegration: boolean;
    allowCoupons: boolean;
    allowCustomization: boolean;
  };
}

interface SubscriptionContextType {
  // Dados da assinatura
  subscription: Subscription | null;
  usageInfo: UsageInfo | null;
  planConfig: PlanConfig | null;
  
  // Estados de carregamento
  isLoading: boolean;
  isError: boolean;
  
  // Funções de verificação
  isFeatureAvailable: (feature: keyof typeof PLAN_CONFIGS.free.limits) => boolean;
  isLimitExceeded: (feature: 'maxProducts' | 'maxOrdersPerMonth', currentCount?: number) => boolean;
  getFeatureLimit: (feature: 'maxProducts' | 'maxOrdersPerMonth') => number;
  
  // Ações
  createCheckoutSession: (interval?: 'month' | 'year') => Promise<string | null>;
  getCustomerPortalUrl: () => Promise<string | null>;
  refreshUsage: () => void;
  
  // Helpers
  canAddProduct: () => boolean;
  canCreateOrder: () => boolean;
  canGeneratePdf: () => boolean;
  canUseAnalytics: () => boolean;
  canUseWhatsapp: () => boolean;
  canUseCoupons: () => boolean;
  canUseCustomization: () => boolean;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export function SubscriptionProvider({ children }: { children: ReactNode }) {
  const { isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  // Query para obter informações de uso
  const {
    data: usageInfo,
    isLoading,
    isError,
    refetch: refreshUsage
  } = useQuery({
    queryKey: ['/api/subscriptions/usage'],
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false,
  });

  // Mutation para criar sessão de checkout
  const createCheckoutMutation = useMutation({
    mutationFn: async (interval: 'month' | 'year' = 'month') => {
      const response = await apiRequest('POST', '/api/subscriptions/create-checkout', { interval });
      const data = await response.json();
      return data.checkoutUrl;
    },
  });

  // Mutation para obter URL do Customer Portal
  const getPortalMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('GET', '/api/subscriptions/portal');
      const data = await response.json();
      return data.portalUrl;
    },
  });

  const subscription = usageInfo?.subscription || null;
  const planConfig = usageInfo?.planConfig || PLAN_CONFIGS.free;

  // Funções de verificação
  const checkFeatureAvailable = (feature: keyof typeof PLAN_CONFIGS.free.limits): boolean => {
    if (!usageInfo) return false;
    return usageInfo.features[feature] as boolean;
  };

  const checkLimitExceeded = (feature: 'maxProducts' | 'maxOrdersPerMonth', currentCount?: number): boolean => {
    if (!usageInfo) return true;
    
    const count = currentCount ?? usageInfo.usage[feature].current;
    const limit = usageInfo.usage[feature].limit;
    
    return limit !== -1 && count >= limit;
  };

  const getLimit = (feature: 'maxProducts' | 'maxOrdersPerMonth'): number => {
    if (!usageInfo) return 0;
    return usageInfo.usage[feature].limit;
  };

  // Funções helper específicas
  const canAddProduct = (): boolean => {
    if (!usageInfo) return false;
    return !usageInfo.usage.products?.isLimitExceeded;
  };

  const canCreateOrder = (): boolean => {
    if (!usageInfo) return false;
    return !usageInfo.usage.orders?.isLimitExceeded;
  };

  const canGeneratePdf = (): boolean => {
    return checkFeatureAvailable('allowPdfGeneration');
  };

  const canUseAnalytics = (): boolean => {
    return checkFeatureAvailable('allowAnalytics');
  };

  const canUseWhatsapp = (): boolean => {
    return checkFeatureAvailable('allowWhatsappIntegration');
  };

  const canUseCoupons = (): boolean => {
    return checkFeatureAvailable('allowCoupons');
  };

  const canUseCustomization = (): boolean => {
    return checkFeatureAvailable('allowCustomization');
  };

  // Ações
  const createCheckoutSession = async (interval: 'month' | 'year' = 'month'): Promise<string | null> => {
    try {
      const url = await createCheckoutMutation.mutateAsync(interval);
      return url;
    } catch (error) {
      console.error('Erro ao criar sessão de checkout:', error);
      return null;
    }
  };

  const getCustomerPortalUrl = async (): Promise<string | null> => {
    try {
      const url = await getPortalMutation.mutateAsync();
      return url;
    } catch (error) {
      console.error('Erro ao obter URL do Customer Portal:', error);
      return null;
    }
  };

  const contextValue: SubscriptionContextType = {
    subscription,
    usageInfo,
    planConfig,
    isLoading,
    isError,
    isFeatureAvailable: checkFeatureAvailable,
    isLimitExceeded: checkLimitExceeded,
    getFeatureLimit: getLimit,
    createCheckoutSession,
    getCustomerPortalUrl,
    refreshUsage,
    canAddProduct,
    canCreateOrder,
    canGeneratePdf,
    canUseAnalytics,
    canUseWhatsapp,
    canUseCoupons,
    canUseCustomization,
  };

  return (
    <SubscriptionContext.Provider value={contextValue}>
      {children}
    </SubscriptionContext.Provider>
  );
}

export function useSubscription(): SubscriptionContextType {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
}

// Hook para verificar funcionalidades específicas
export function useFeatureCheck(feature: keyof typeof PLAN_CONFIGS.free.limits) {
  const { isFeatureAvailable } = useSubscription();
  return isFeatureAvailable(feature);
}

// Hook para verificar limites específicos
export function useLimitCheck(feature: 'maxProducts' | 'maxOrdersPerMonth') {
  const { isLimitExceeded, getFeatureLimit, usageInfo } = useSubscription();
  
  const current = usageInfo?.usage[feature].current || 0;
  const limit = getFeatureLimit(feature);
  const exceeded = isLimitExceeded(feature);
  
  return {
    current,
    limit,
    exceeded,
    remaining: limit === -1 ? Infinity : Math.max(0, limit - current),
    percentage: limit === -1 ? 0 : Math.min(100, (current / limit) * 100),
  };
}
