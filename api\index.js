// Vercel Serverless Function
// Este arquivo redireciona todas as requisições da API para o servidor Express

// Importa o app Express compilado
let app;

export default async function handler(req, res) {
  // Lazy load do app Express para otimizar cold starts
  if (!app) {
    try {
      // Importa dinamicamente o servidor Express compilado
      const serverModule = await import('../dist/api/index.js');
      app = serverModule.default || serverModule.app || serverModule;

      console.log('App carregado:', typeof app);
    } catch (error) {
      console.error('Erro ao carregar o servidor Express:', error);
      return res.status(500).json({
        error: 'Erro interno do servidor',
        details: error.message
      });
    }
  }

  // Se ainda não temos um app válido, retorna erro
  if (!app || typeof app !== 'function') {
    console.error('App Express não é uma função válida:', typeof app);
    return res.status(500).json({
      error: 'Configuração do servidor inválida',
      appType: typeof app
    });
  }

  // Processa a requisição através do Express
  try {
    app(req, res);
  } catch (error) {
    console.error('Erro ao processar requisição:', error);
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Erro ao processar requisição',
        details: error.message
      });
    }
  }
}
