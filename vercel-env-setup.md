# Configuração de Variáveis de Ambiente no Vercel

## Variáveis Obrigatórias

Configure as seguintes variáveis de ambiente no painel do Vercel:

### Database (Supabase)
```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### Firebase
```
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
FIREBASE_APP_ID=your_firebase_app_id
```

### Stripe
```
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
```

### SendGrid
```
SENDGRID_API_KEY=your_sendgrid_api_key
```

### Aplicação
```
NODE_ENV=production
SESSION_SECRET=your_session_secret
```

## Como Configurar

1. Acesse o painel do Vercel
2. Vá para Settings > Environment Variables
3. Adicione cada variável acima com seus valores correspondentes
4. Certifique-se de marcar todas as opções (Production, Preview, Development)

## Comandos para Deploy

```bash
# Deploy para preview
vercel

# Deploy para production
vercel --prod
```
