/**
 * Rotas administrativas para corrigir cálculos de desconto
 */
import { Request, Response } from 'express';
import { fixPercentageDiscounts } from '../scripts/fix-percentage-discounts.js';
import { fixAllRevisionCalculations } from '../utils/fix-revision-calculations';

/**
 * Endpoint para corrigir os percentuais originais em descontos do tipo percentual
 */
export async function fixPercentageDiscountsEndpoint(req: Request, res: Response) {
  try {
    console.log('Iniciando correção de descontos percentuais via endpoint');
    
    const fixedCount = await fixPercentageDiscounts();
    
    return res.status(200).json({
      success: true,
      message: `Correção de descontos percentuais concluída. ${fixedCount} revisões atualizadas.`,
      fixedCount
    });
  } catch (error) {
    console.error('Erro ao executar correção de descontos percentuais:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao executar correção de descontos percentuais',
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Endpoint para corrigir cálculos em todas as revisões
 */
export async function fixAllRevisionsEndpoint(req: Request, res: Response) {
  try {
    console.log('Iniciando correção de cálculos em todas as revisões via endpoint');
    
    const fixedCount = await fixAllRevisionCalculations();
    
    return res.status(200).json({
      success: true,
      message: `Correção de cálculos concluída. ${fixedCount} revisões atualizadas.`,
      fixedCount
    });
  } catch (error) {
    console.error('Erro ao executar correção de cálculos:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao executar correção de cálculos',
      error: error instanceof Error ? error.message : String(error)
    });
  }
}